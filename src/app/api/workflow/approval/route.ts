/**
 * Workflow Artifact Approval API
 * Handle artifact approval decisions and workflow continuation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../core/workflow/singleton';
import { ApprovalDecision } from '../../../../core/workflow/types';

/**
 * POST /api/workflow/approval
 * Submit approval decision for an artifact
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { artifactId, approved, approver, feedback, reason } = body;

    // Validate required fields
    if (!artifactId) {
      return NextResponse.json(
        { error: 'Artifact ID is required' },
        { status: 400 }
      );
    }

    if (typeof approved !== 'boolean') {
      return NextResponse.json(
        { error: 'Approval decision (approved) is required' },
        { status: 400 }
      );
    }

    if (!approver) {
      return NextResponse.json(
        { error: 'Approver is required' },
        { status: 400 }
      );
    }

    const workflowEngine = getWorkflowEngine();

    // Create approval decision
    const decision: ApprovalDecision = {
      approved,
      approver,
      feedback: feedback || '',
      timestamp: new Date().toISOString(),
      reason: reason || ''
    };

    // Submit approval decision
    await workflowEngine.submitApproval(artifactId, decision);

    // Get updated approval status
    const approvalStatus = await workflowEngine.getApprovalStatus(artifactId);

    return NextResponse.json({
      success: true,
      data: {
        artifactId,
        decision,
        approvalStatus,
        message: approved ? 'Artifact approved successfully' : 'Artifact rejected'
      }
    });

  } catch (error) {
    console.error('Approval submission error:', error);

    return NextResponse.json(
      {
        error: 'Failed to submit approval decision',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/workflow/approval?artifactId=xxx
 * Get approval status for an artifact
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const artifactId = searchParams.get('artifactId');
    const userId = searchParams.get('userId'); // For getting pending approvals

    const workflowEngine = getWorkflowEngine();

    if (artifactId) {
      // Get specific artifact approval status
      const approvalStatus = await workflowEngine.getApprovalStatus(artifactId);
      const artifact = await workflowEngine.getArtifact(artifactId);

      if (!artifact) {
        return NextResponse.json(
          { error: 'Artifact not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          artifact,
          approvalStatus
        }
      });
    }

    if (userId) {
      // Get pending approvals for user
      // Note: This would need to be implemented in WorkflowManager
      // For now, return empty array
      return NextResponse.json({
        success: true,
        data: {
          pendingApprovals: []
        }
      });
    }

    return NextResponse.json(
      { error: 'Either artifactId or userId parameter is required' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Approval status retrieval error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve approval status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/workflow/approval
 * Update artifact and request re-approval
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { artifactId, updates, requestReapproval } = body;

    if (!artifactId) {
      return NextResponse.json(
        { error: 'Artifact ID is required' },
        { status: 400 }
      );
    }

    const workflowEngine = getWorkflowEngine();

    // Update artifact
    await workflowEngine.updateArtifact(artifactId, {
      ...updates,
      updatedAt: new Date().toISOString()
    });

    // If requesting re-approval, reset status
    if (requestReapproval) {
      await workflowEngine.updateArtifact(artifactId, {
        status: 'pending_approval' as any,
        approvedBy: undefined,
        approvedAt: undefined,
        rejectedBy: undefined,
        rejectedAt: undefined,
        rejectionReason: undefined
      });
    }

    const artifact = await workflowEngine.getArtifact(artifactId);

    return NextResponse.json({
      success: true,
      data: {
        artifact,
        message: requestReapproval ? 'Artifact updated and re-approval requested' : 'Artifact updated successfully'
      }
    });

  } catch (error) {
    console.error('Artifact update error:', error);

    return NextResponse.json(
      {
        error: 'Failed to update artifact',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

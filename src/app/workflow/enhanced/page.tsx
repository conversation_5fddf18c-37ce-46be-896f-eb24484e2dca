/**
 * Enhanced Workflow Interface
 * Combines traditional workflow interface with visual workflow builder
 */

'use client';

import { useState, useEffect } from 'react';
import WorkflowInterface from '../../../components/Workflow/WorkflowInterface';
import SimpleVisualWorkflow from '../../../components/Workflow/SimpleVisualWorkflow';
import ApprovalFlowExplanation from '../../../components/Workflow/ApprovalFlowExplanation';

interface ExecutionStatus {
  id: string;
  workflowId: string;
  status: string;
  progress: number;
  currentStep?: string;
  startedAt: string;
  completedAt?: string;
  error?: any;
}

export default function EnhancedWorkflowPage() {
  const [activeView, setActiveView] = useState<'interface' | 'visual' | 'review' | 'history'>('interface');
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [workflowHistory, setWorkflowHistory] = useState<ExecutionStatus[]>([]);
  const [showApprovalHelp, setShowApprovalHelp] = useState(false);

  // Load workflow history on mount
  useEffect(() => {
    loadWorkflowHistory();
  }, []);

  const loadWorkflowHistory = async () => {
    try {
      const response = await fetch('/api/workflow/history');
      const result = await response.json();

      if (result.success) {
        setWorkflowHistory(result.data.executions || []);
      }
    } catch (error) {
      console.error('Failed to load workflow history:', error);
    }
  };

  const handleWorkflowCreated = (executionId: string) => {
    // Store execution ID for visual workflow
    setCurrentWorkflowId(executionId);
    setActiveView('visual');

    // Refresh history to show new workflow
    loadWorkflowHistory();
  };

  const handleReviewCreated = (reviewId: string) => {
    console.log('Review created:', reviewId);
    // For now, just log the review creation
    // In the future, this could navigate to the review interface
  };

  const handleNodeClick = (nodeId: string, nodeType: string) => {
    console.log('Node clicked:', nodeId, nodeType);

    if (nodeType === 'review') {
      // For now, just log the click
      // In the future, this could navigate to the review interface
      console.log('Review node clicked:', nodeId);
    }
  };

  const handleWorkflowUpdate = (steps: any[]) => {
    console.log('Workflow updated:', steps.length, 'steps');
    // Could save workflow layout here
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Enhanced Workflow System</h1>
              <p className="text-sm text-gray-600">
                Create, visualize, and manage content workflows with advanced review capabilities
              </p>
            </div>

            {/* Help Button */}
            <button
              onClick={() => setShowApprovalHelp(true)}
              className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              How Approvals Work
            </button>
            
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveView('interface')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeView === 'interface'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                📝 Workflow Builder
              </button>
              <button
                onClick={() => setActiveView('visual')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeView === 'visual'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                🎨 Visual Workflow
              </button>
              <button
                onClick={() => setActiveView('review')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeView === 'review'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                👥 Review System
              </button>
              <button
                onClick={() => setActiveView('history')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeView === 'history'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                📊 History
              </button>
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className={`${activeView === 'visual' ? 'h-screen' : ''}`}>
        {activeView === 'interface' && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <WorkflowInterface 
              onWorkflowCreated={handleWorkflowCreated}
              onReviewCreated={handleReviewCreated}
            />
          </div>
        )}

        {activeView === 'visual' && (
          <div className="h-full">
            <SimpleVisualWorkflow
              workflowId={currentWorkflowId || undefined}
              onStepClick={handleNodeClick}
              onWorkflowUpdate={handleWorkflowUpdate}
            />
          </div>
        )}

        {activeView === 'review' && (
          <div className="h-full">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="bg-white rounded-lg shadow p-6 text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Review System</h3>
                <p className="text-gray-600 mb-4">
                  The review system is integrated into the workflow execution. When a workflow reaches an approval gate,
                  you'll be redirected to the approval interface automatically.
                </p>
                <div className="space-y-4">
                  <div className="text-left bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">How Approval Gates Work:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Create a workflow with approval steps (e.g., "SEO Blog Post with Approval Gates")</li>
                      <li>• Workflow will pause at approval gates and create artifacts for review</li>
                      <li>• You'll receive an approval URL to review and approve/reject the artifact</li>
                      <li>• Once approved, the workflow continues automatically</li>
                    </ul>
                  </div>
                  <button
                    onClick={() => setActiveView('interface')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Workflow with Approval Gates
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'history' && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">Workflow History</h2>
                <p className="text-gray-600 mt-1">View and manage your workflow executions</p>
              </div>
              
              <div className="p-6">
                {workflowHistory.length > 0 ? (
                  <div className="space-y-4">
                    {workflowHistory.map((execution) => (
                      <div key={execution.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <div className="font-medium text-gray-900">
                              Workflow {execution.workflowId}
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              execution.status === 'completed' ? 'bg-green-100 text-green-800' :
                              execution.status === 'failed' ? 'bg-red-100 text-red-800' :
                              execution.status === 'running' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {execution.status}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(execution.startedAt).toLocaleDateString()}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Progress:</span> {execution.progress}%
                          </div>
                          {execution.currentStep && (
                            <div>
                              <span className="font-medium">Current Step:</span> {execution.currentStep}
                            </div>
                          )}
                          {execution.completedAt && (
                            <div>
                              <span className="font-medium">Completed:</span> {new Date(execution.completedAt).toLocaleString()}
                            </div>
                          )}
                        </div>

                        {execution.error && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                            <div className="text-sm font-medium text-red-800">Error:</div>
                            <div className="text-sm text-red-600 mt-1">{execution.error.message}</div>
                          </div>
                        )}

                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => {
                              setCurrentWorkflowId(execution.id);
                              setActiveView('visual');
                            }}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                          >
                            View Visual
                          </button>
                          {execution.status === 'completed' && (
                            <a
                              href={`/workflow/results/${execution.id}`}
                              className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                            >
                              View Results
                            </a>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Workflows Yet</h3>
                    <p className="text-gray-600 mb-4">
                      Create your first workflow to see execution history here.
                    </p>
                    <button
                      onClick={() => setActiveView('interface')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Create First Workflow
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

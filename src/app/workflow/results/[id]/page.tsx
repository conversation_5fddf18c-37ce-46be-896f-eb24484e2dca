/**
 * Workflow Results Page
 * Display the generated content from a completed workflow
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface WorkflowResults {
  execution: {
    id: string;
    workflowId: string;
    status: string;
    progress: number;
    startedAt: string;
    completedAt?: string;
    inputs: Record<string, any>;
  };
  workflow: {
    id: string;
    name: string;
    description: string;
  } | null;
  steps: Array<{
    stepId: string;
    status: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
    error?: string;
  }>;
  content: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  }>;
}

export default function WorkflowResultsPage() {
  const params = useParams();
  const router = useRouter();
  const executionId = params.id as string;

  const [results, setResults] = useState<WorkflowResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [reviewData, setReviewData] = useState<Record<string, { rating: number; feedback: string; approved: boolean }>>({});
  const [submittingReview, setSubmittingReview] = useState<string | null>(null);

  useEffect(() => {
    loadResults();
  }, [executionId]);

  const loadResults = async () => {
    try {
      const response = await fetch(`/api/workflow/results/${executionId}`);
      const result = await response.json();

      if (result.success) {
        setResults(result.data);
      } else {
        setError(result.error || 'Failed to load results');
      }
    } catch (err) {
      setError('Failed to load results');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatContent = (content: any): string => {
    if (typeof content === 'string') {
      return content;
    }
    return JSON.stringify(content, null, 2);
  };

  const formatDuration = (duration?: number): string => {
    if (!duration) return 'N/A';
    return `${(duration / 1000).toFixed(1)}s`;
  };

  const updateReviewData = (contentId: string, field: string, value: any) => {
    setReviewData(prev => ({
      ...prev,
      [contentId]: {
        ...prev[contentId],
        [field]: value
      }
    }));
  };

  const submitReview = async (contentId: string, approved: boolean) => {
    setSubmittingReview(contentId);

    try {
      const review = reviewData[contentId] || { rating: 3, feedback: '', approved: false };

      const response = await fetch(`/api/review/${contentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...review,
          approved,
          executionId
        })
      });

      const result = await response.json();

      if (result.success) {
        // Reload results to show updated status
        await loadResults();
        setError('');
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('Failed to submit review');
      console.error(err);
    } finally {
      setSubmittingReview(null);
    }
  };

  const needsReview = (content: any): boolean => {
    return content.status === 'pending_review' || content.status === 'draft';
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-gray-600">No results found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Workflow Results</h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Execution ID: {results.execution.id}</span>
              <span>Status: <span className={`font-medium ${
                results.execution.status === 'completed' ? 'text-green-600' :
                results.execution.status === 'failed' ? 'text-red-600' :
                'text-blue-600'
              }`}>{results.execution.status}</span></span>
              <span>Progress: {results.execution.progress}%</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => router.push('/workflow')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ← Back to Workflow
            </button>
            <button
              onClick={() => router.push('/dashboard')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Dashboard
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Workflow Info */}
      {results.workflow && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Workflow: {results.workflow.name}</h3>
          <p className="text-sm text-gray-600">{results.workflow.description}</p>
        </div>
      )}

      {/* Execution Summary */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h3 className="font-medium mb-3">Execution Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Started:</span>
            <p>{new Date(results.execution.startedAt).toLocaleString()}</p>
          </div>
          {results.execution.completedAt && (
            <div>
              <span className="text-gray-500">Completed:</span>
              <p>{new Date(results.execution.completedAt).toLocaleString()}</p>
            </div>
          )}
          <div>
            <span className="text-gray-500">Total Steps:</span>
            <p>{results.steps.length}</p>
          </div>
          <div>
            <span className="text-gray-500">Content Items:</span>
            <p>{results.content.length}</p>
          </div>
        </div>
      </div>

      {/* Review Status Summary */}
      {results.content.length > 0 && (
        <div className="bg-white border rounded-lg p-4 mb-6">
          <h3 className="font-medium mb-3">Review Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Pending Review:</span>
              <p className="text-yellow-600 font-medium">
                {results.content.filter(c => needsReview(c)).length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Approved:</span>
              <p className="text-green-600 font-medium">
                {results.content.filter(c => c.status === 'approved').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Rejected:</span>
              <p className="text-red-600 font-medium">
                {results.content.filter(c => c.status === 'rejected').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Overall Progress:</span>
              <p className="font-medium">
                {Math.round((results.content.filter(c => c.status === 'approved').length / results.content.length) * 100)}% Complete
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Generated Content */}
      {results.content.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4">Generated Content</h3>
          <div className="space-y-4">
            {results.content.map(content => (
              <div key={content.id} className="bg-white border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{content.title}</h4>
                  <div className="flex gap-2 text-xs">
                    <span className="bg-gray-100 px-2 py-1 rounded">{content.type}</span>
                    <span className={`px-2 py-1 rounded ${
                      content.status === 'approved' ? 'bg-green-100 text-green-700' :
                      content.status === 'rejected' ? 'bg-red-100 text-red-700' :
                      'bg-yellow-100 text-yellow-700'
                    }`}>
                      {content.status}
                    </span>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded border">
                  <pre className="whitespace-pre-wrap text-sm">
                    {formatContent(content.content)}
                  </pre>
                </div>

                {/* Human Review Section */}
                {needsReview(content) && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h5 className="font-medium text-yellow-800 mb-3">Human Review Required</h5>

                    {/* Rating */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Quality Rating (1-5)
                      </label>
                      <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map(rating => (
                          <button
                            key={rating}
                            onClick={() => updateReviewData(content.id, 'rating', rating)}
                            className={`w-8 h-8 rounded ${
                              (reviewData[content.id]?.rating || 3) >= rating
                                ? 'bg-yellow-400 text-white'
                                : 'bg-gray-200 text-gray-600'
                            } hover:bg-yellow-300 transition-colors`}
                          >
                            ★
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Feedback */}
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Feedback & Comments
                      </label>
                      <textarea
                        value={reviewData[content.id]?.feedback || ''}
                        onChange={(e) => updateReviewData(content.id, 'feedback', e.target.value)}
                        placeholder="Provide feedback on the content quality, accuracy, and any improvements needed..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={3}
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={() => submitReview(content.id, true)}
                        disabled={submittingReview === content.id}
                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {submittingReview === content.id ? 'Submitting...' : 'Approve'}
                      </button>
                      <button
                        onClick={() => submitReview(content.id, false)}
                        disabled={submittingReview === content.id}
                        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {submittingReview === content.id ? 'Submitting...' : 'Reject'}
                      </button>
                    </div>
                  </div>
                )}

                {content.metadata && (
                  <div className="mt-3 text-xs text-gray-500">
                    <div className="flex gap-4">
                      {content.metadata.wordCount && (
                        <span>Words: {content.metadata.wordCount}</span>
                      )}
                      {content.metadata.aiModel && (
                        <span>Model: {content.metadata.aiModel}</span>
                      )}
                      {content.metadata.cost && (
                        <span>Cost: ${content.metadata.cost.toFixed(4)}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Step Details */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Step Details</h3>
        <div className="space-y-3">
          {results.steps.map(step => (
            <div key={step.stepId} className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{step.stepId}</h4>
                <div className="flex gap-2 text-xs">
                  <span className={`px-2 py-1 rounded ${
                    step.status === 'completed' ? 'bg-green-100 text-green-700' :
                    step.status === 'failed' ? 'bg-red-100 text-red-700' :
                    step.status === 'running' ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {step.status}
                  </span>
                  <span className="bg-gray-100 px-2 py-1 rounded">
                    {formatDuration(step.duration)}
                  </span>
                </div>
              </div>

              {step.error && (
                <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                  Error: {step.error}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 font-medium">Inputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {JSON.stringify(step.inputs, null, 2)}
                  </pre>
                </div>
                <div>
                  <span className="text-gray-500 font-medium">Outputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {JSON.stringify(step.outputs, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

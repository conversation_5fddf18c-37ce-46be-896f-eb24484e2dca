/**
 * Simplified State Management Types
 * Flat state structure as recommended in the critical architecture changes
 */

import { WorkflowExecution, Workflow } from '../workflow/types';

// Simplified State Structure (flattened as per requirements)
export interface SimplifiedState {
  // Core entities with flat structure
  workflows: Record<string, Workflow>;
  executions: Record<string, WorkflowExecution>;
  content: Record<string, ContentItem>;
  reviews: Record<string, Review>;
  
  // System state
  system: SystemState;
  
  // Metadata
  lastUpdated: string;
  version: number;
}

// Content items (artifacts in simplified form)
export interface ContentItem {
  id: string;
  type: ContentType;
  title: string;
  content: any; // Can be string, object, etc.
  status: ContentStatus;
  executionId: string;
  stepId: string;
  createdAt: string;
  updatedAt: string;
  metadata: ContentMetadata;
}

export enum ContentType {
  BLOG_POST = 'blog_post',
  PRODUCT_DESCRIPTION = 'product_description',
  KEYWORD_RESEARCH = 'keyword_research',
  SEO_ANALYSIS = 'seo_analysis',
  CONTENT_ANALYSIS = 'content_analysis',
  EMAIL = 'email',
  SOCIAL_POST = 'social_post',
  GENERIC = 'generic'
}

export enum ContentStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PUBLISHED = 'published'
}

export interface ContentMetadata {
  wordCount?: number;
  language?: string;
  tags?: string[];
  seoScore?: number;
  qualityScore?: number;
  aiModel?: string;
  aiProvider?: string;
  cost?: number;
}

// Simplified Review System
export interface Review {
  id: string;
  contentId: string;
  executionId: string;
  stepId: string;
  type: ReviewType;
  status: ReviewStatus;
  reviewer?: string;
  instructions: string;
  decision?: ReviewDecision;
  feedback?: string;
  edits?: Record<string, any>;
  deadline?: string;
  createdAt: string;
  completedAt?: string;
}

export enum ReviewType {
  APPROVAL = 'approval',
  EDITING = 'editing',
  FEEDBACK = 'feedback'
}

export enum ReviewStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  EXPIRED = 'expired'
}

export enum ReviewDecision {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  NEEDS_CHANGES = 'needs_changes'
}

// System State
export interface SystemState {
  // Essential events only (reduced from 20+ to 6)
  events: SystemEvent[];
  
  // Simple error tracking
  errors: SystemError[];
  
  // Usage statistics
  usage: UsageStats;
  
  // Configuration
  config: SystemConfig;
}

// Essential Events (reduced set)
export interface SystemEvent {
  id: string;
  type: EssentialEventType;
  timestamp: string;
  data: any;
}

export enum EssentialEventType {
  // Workflow lifecycle
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  
  // Human interaction
  REVIEW_NEEDED = 'review.needed',
  REVIEW_COMPLETE = 'review.complete',
  
  // Content
  CONTENT_READY = 'content.ready',
  CONTENT_PUBLISHED = 'content.published'
}

// Simple Error Handling
export interface SystemError {
  id: string;
  type: 'workflow' | 'ai' | 'review' | 'system';
  message: string;
  context: any;
  timestamp: string;
  resolved: boolean;
}

// Usage Statistics
export interface UsageStats {
  totalWorkflows: number;
  totalExecutions: number;
  totalContent: number;
  totalReviews: number;
  aiUsage: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    byProvider: Record<string, ProviderUsage>;
  };
  lastReset: string;
}

export interface ProviderUsage {
  requests: number;
  tokens: number;
  cost: number;
  averageResponseTime: number;
}

// System Configuration
export interface SystemConfig {
  // AI settings
  defaultAIProvider: string;
  defaultAIModel: string;
  
  // Review settings
  defaultReviewTimeout: number; // in hours
  autoApprovalThreshold?: number; // quality score threshold
  
  // Notification settings
  emailNotifications: boolean;
  webhookUrl?: string;
  
  // Rate limiting
  rateLimits: {
    workflowsPerHour: number;
    aiRequestsPerMinute: number;
  };
}

// State Store Interface (simplified)
export interface ISimplifiedStateStore {
  // Basic CRUD operations
  get(): Promise<SimplifiedState | null>;
  set(state: SimplifiedState): Promise<void>;
  update(updateFn: (state: SimplifiedState | null) => SimplifiedState | null): Promise<void>;
  
  // Entity-specific operations
  getWorkflow(id: string): Promise<Workflow | null>;
  setWorkflow(workflow: Workflow): Promise<void>;
  
  getExecution(id: string): Promise<WorkflowExecution | null>;
  setExecution(execution: WorkflowExecution): Promise<void>;
  getAllExecutions(): Promise<WorkflowExecution[]>;
  deleteExecution(id: string): Promise<boolean>;
  
  getContent(id: string): Promise<ContentItem | null>;
  setContent(content: ContentItem): Promise<void>;
  
  getReview(id: string): Promise<Review | null>;
  setReview(review: Review): Promise<void>;
  
  // Event operations
  addEvent(event: Omit<SystemEvent, 'id' | 'timestamp'>): Promise<void>;
  getEvents(limit?: number): Promise<SystemEvent[]>;
  
  // Error operations
  addError(error: Omit<SystemError, 'id' | 'timestamp'>): Promise<void>;
  getErrors(resolved?: boolean): Promise<SystemError[]>;
  resolveError(id: string): Promise<void>;
  
  // Usage tracking
  updateUsage(updates: Partial<UsageStats>): Promise<void>;
  getUsage(): Promise<UsageStats>;

  // Workflow layout management
  updateWorkflowLayout(workflowId: string, layout: { nodes: any[]; edges: any[] }): Promise<void>;
  updateStepStatus(workflowId: string, stepId: string, status: string, progress?: number): Promise<void>;
}

// Storage Adapter Interface
export interface IStorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  delete(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  list(prefix?: string): Promise<string[]>;
}

// Memory Storage Adapter (for development)
export class MemoryStorageAdapter implements IStorageAdapter {
  private storage = new Map<string, any>();

  async get(key: string): Promise<any> {
    return this.storage.get(key) || null;
  }

  async set(key: string, value: any): Promise<void> {
    this.storage.set(key, value);
  }

  async delete(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    return this.storage.has(key);
  }

  async list(prefix?: string): Promise<string[]> {
    const keys = Array.from(this.storage.keys());
    return prefix ? keys.filter(key => key.startsWith(prefix)) : keys;
  }
}

// Redis Storage Adapter (for production)
export interface RedisStorageConfig {
  url: string;
  keyPrefix?: string;
  ttl?: number; // Time to live in seconds
}

// State validation
export interface StateValidator {
  validate(state: SimplifiedState): ValidationResult;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

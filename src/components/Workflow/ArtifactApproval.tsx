/**
 * Artifact Approval Component
 * Handle approval/rejection of workflow artifacts
 */

'use client';

import { useState, useEffect } from 'react';

interface Artifact {
  id: string;
  stepId: string;
  executionId: string;
  type: string;
  title: string;
  content: any;
  status: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface ApprovalStatus {
  artifactId: string;
  status: string;
  approvals: any[];
  requiredApprovals: number;
  pendingApprovers: string[];
  canProceed: boolean;
  escalated: boolean;
  escalationLevel: number;
}

interface ArtifactApprovalProps {
  artifactId: string;
  onApprovalComplete?: (artifactId: string, approved: boolean) => void;
  currentUser?: string;
}

export default function ArtifactApproval({
  artifactId,
  onApprovalComplete,
  currentUser = 'default-user'
}: ArtifactApprovalProps) {
  const [artifact, setArtifact] = useState<Artifact | null>(null);
  const [approvalStatus, setApprovalStatus] = useState<ApprovalStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [feedback, setFeedback] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | ''>('');

  useEffect(() => {
    loadArtifactData();
  }, [artifactId]);

  const loadArtifactData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workflow/approval?artifactId=${artifactId}`);
      const result = await response.json();

      if (result.success) {
        setArtifact(result.data.artifact);
        setApprovalStatus(result.data.approvalStatus);
      } else {
        setError(result.error || 'Failed to load artifact data');
      }
    } catch (err) {
      setError('Failed to load artifact data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const submitApproval = async () => {
    if (!decision) {
      setError('Please select approve or reject');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/workflow/approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          approved: decision === 'approve',
          approver: currentUser,
          feedback,
          reason: decision === 'reject' ? feedback : undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        setApprovalStatus(result.data.approvalStatus);
        
        if (onApprovalComplete) {
          onApprovalComplete(artifactId, decision === 'approve');
        }

        // Reset form
        setDecision('');
        setFeedback('');
      } else {
        setError(result.error || 'Failed to submit approval');
      }
    } catch (err) {
      setError('Failed to submit approval');
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  const renderArtifactContent = () => {
    if (!artifact) return null;

    // Handle different artifact types
    switch (artifact.type) {
      case 'keyword_research':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Keyword Research Results</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)
                }
              </pre>
            </div>
          </div>
        );
      
      case 'content_draft':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Content Draft</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="prose max-w-none">
                {typeof artifact.content === 'string' 
                  ? <div dangerouslySetInnerHTML={{ __html: artifact.content }} />
                  : <pre className="whitespace-pre-wrap text-sm">
                      {JSON.stringify(artifact.content, null, 2)}
                    </pre>
                }
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{artifact.title}</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)
                }
              </pre>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading artifact...</span>
      </div>
    );
  }

  if (!artifact) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">Artifact not found</p>
      </div>
    );
  }

  const isAlreadyProcessed = approvalStatus?.status === 'approved' || approvalStatus?.status === 'rejected';
  const canApprove = !isAlreadyProcessed && approvalStatus?.pendingApprovers.includes(currentUser);

  // Debug logging
  console.log('Approval Debug:', {
    artifactId,
    currentUser,
    approvalStatus,
    isAlreadyProcessed,
    canApprove,
    pendingApprovers: approvalStatus?.pendingApprovers
  });

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Artifact Approval</h1>
            <p className="text-gray-600">Review and approve workflow artifact</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              approvalStatus?.status === 'approved' ? 'bg-green-100 text-green-800' :
              approvalStatus?.status === 'rejected' ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }`}>
              {approvalStatus?.status || 'pending'}
            </span>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Artifact Details */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
          <div>
            <span className="font-medium text-gray-700">Type:</span>
            <span className="ml-2 text-gray-900">{artifact.type}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created:</span>
            <span className="ml-2 text-gray-900">{new Date(artifact.createdAt).toLocaleString()}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Version:</span>
            <span className="ml-2 text-gray-900">{artifact.version}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created by:</span>
            <span className="ml-2 text-gray-900">{artifact.createdBy}</span>
          </div>
        </div>

        {/* Artifact Content */}
        {renderArtifactContent()}
      </div>

      {/* Approval Actions */}
      {canApprove && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Approval</h3>
          
          <div className="space-y-4">
            {/* Decision */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Decision
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="decision"
                    value="approve"
                    checked={decision === 'approve'}
                    onChange={(e) => setDecision(e.target.value as 'approve')}
                    className="mr-2"
                  />
                  <span className="text-green-700">Approve</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="decision"
                    value="reject"
                    checked={decision === 'reject'}
                    onChange={(e) => setDecision(e.target.value as 'reject')}
                    className="mr-2"
                  />
                  <span className="text-red-700">Reject</span>
                </label>
              </div>
            </div>

            {/* Feedback */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {decision === 'reject' ? 'Rejection Reason' : 'Feedback (Optional)'}
              </label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={decision === 'reject' ? 'Please explain why this artifact is being rejected...' : 'Any feedback or comments...'}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                onClick={submitApproval}
                disabled={submitting || !decision}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  decision === 'approve'
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : decision === 'reject'
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {submitting ? 'Submitting...' : `${decision === 'approve' ? 'Approve' : 'Reject'} Artifact`}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Already Processed */}
      {isAlreadyProcessed && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Approval Status</h3>
          <p className="text-gray-600">
            This artifact has already been {approvalStatus?.status}. 
            {approvalStatus?.status === 'approved' && ' The workflow will continue automatically.'}
            {approvalStatus?.status === 'rejected' && ' The workflow has been stopped.'}
          </p>
        </div>
      )}
    </div>
  );
}

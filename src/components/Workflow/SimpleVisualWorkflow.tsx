/**
 * Simple Visual Workflow
 * A custom workflow visualization without external dependencies
 */

'use client';

import { useState, useEffect } from 'react';

interface WorkflowStep {
  id: string;
  name: string;
  type: 'template' | 'input' | 'ai_generation' | 'review' | 'output' | 'approval_gate';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'waiting_approval' | 'approved' | 'rejected';
  description?: string;
  progress?: number;
  icon?: string;
  reviewers?: string[];
  priority?: string;
  artifactId?: string; // For approval gates
  approvalUrl?: string; // For approval gates
}

interface SimpleVisualWorkflowProps {
  workflowId?: string; // This is actually execution ID
  onStepClick?: (stepId: string, stepType: string) => void;
  onWorkflowUpdate?: (steps: WorkflowStep[]) => void;
  readOnly?: boolean;
}

export default function SimpleVisualWorkflow({
  workflowId,
  onStepClick,
  onWorkflowUpdate,
  readOnly = false
}: SimpleVisualWorkflowProps) {
  const [steps, setSteps] = useState<WorkflowStep[]>([
    {
      id: 'template',
      name: 'Template Selection',
      type: 'template',
      status: 'completed',
      description: 'Choose workflow template',
      icon: '📋'
    },
    {
      id: 'input',
      name: 'Input Configuration',
      type: 'input',
      status: 'completed',
      description: 'Configure workflow inputs',
      icon: '⚙️'
    },
    {
      id: 'ai-generation',
      name: 'AI Content Generation',
      type: 'ai_generation',
      status: 'running',
      description: 'Generate content using AI',
      progress: 65,
      icon: '🤖'
    },
    {
      id: 'review',
      name: 'Human Review',
      type: 'review',
      status: 'pending',
      description: 'Review and approve content',
      reviewers: ['reviewer-1', 'reviewer-2'],
      priority: 'medium',
      icon: '👥'
    },
    {
      id: 'output',
      name: 'Output Generation',
      type: 'output',
      status: 'pending',
      description: 'Generate final output',
      icon: '📄'
    }
  ]);

  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (workflowId) {
      loadWorkflowData(workflowId);

      // Set up polling for real-time updates
      const interval = setInterval(() => {
        loadWorkflowData(workflowId);
      }, 3000); // Poll every 3 seconds

      return () => clearInterval(interval);
    }
  }, [workflowId]);

  useEffect(() => {
    if (onWorkflowUpdate) {
      onWorkflowUpdate(steps);
    }
  }, [steps, onWorkflowUpdate]);

  const loadWorkflowData = async (executionId: string) => {
    try {
      // Use the workflow create API to get execution status
      const response = await fetch(`/api/workflow/create?executionId=${executionId}`);
      const result = await response.json();

      if (result.success && result.data.execution) {
        const execution = result.data.execution;
        const stepResults = result.data.steps || [];

        // Convert step results to visual workflow steps
        const workflowSteps: WorkflowStep[] = [];

        // Add template selection step
        workflowSteps.push({
          id: 'template',
          name: 'Template Selection',
          type: 'template',
          status: 'completed',
          description: 'Workflow template selected',
          icon: '📋'
        });

        // Add input configuration step
        workflowSteps.push({
          id: 'input',
          name: 'Input Configuration',
          type: 'input',
          status: 'completed',
          description: 'Workflow inputs configured',
          icon: '⚙️'
        });

        // Add actual workflow steps from execution
        stepResults.forEach((stepResult: any) => {
          let stepType: any = 'ai_generation';
          let icon = '🤖';
          let description = `Step ${stepResult.status}`;
          let approvalUrl = undefined;

          // Determine step type and icon based on step ID or status
          if (stepResult.stepId.includes('approval') || stepResult.status === 'waiting_approval' || stepResult.approvalRequired) {
            stepType = 'approval_gate';
            icon = '✅';
            description = stepResult.status === 'waiting_approval' ? 'Waiting for approval' :
                         stepResult.status === 'approved' ? 'Approved' :
                         stepResult.status === 'rejected' ? 'Rejected' : 'Approval gate';
            if (stepResult.artifactId) {
              approvalUrl = `/workflow/approval/${stepResult.artifactId}`;
            }
          } else if (stepResult.stepId.includes('review')) {
            stepType = 'review';
            icon = '👥';
            description = 'Human review required';
          } else if (stepResult.stepId.includes('output') || stepResult.stepId.includes('final')) {
            stepType = 'output';
            icon = '📄';
            description = 'Generate final output';
          } else if (stepResult.stepId.includes('keyword')) {
            description = 'Keyword research and analysis';
          } else if (stepResult.stepId.includes('content')) {
            description = 'Content generation';
          }

          workflowSteps.push({
            id: stepResult.stepId,
            name: stepResult.stepId.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
            type: stepType,
            status: stepResult.status,
            description,
            progress: stepResult.status === 'running' ? 50 : undefined,
            icon,
            artifactId: stepResult.artifactId,
            approvalUrl
          });
        });

        // If no steps from execution, show default workflow progress
        if (stepResults.length === 0) {
          workflowSteps.push({
            id: 'processing',
            name: 'Processing',
            type: 'ai_generation',
            status: execution.status === 'running' ? 'running' : execution.status,
            description: `Workflow ${execution.status}`,
            progress: execution.progress || 0,
            icon: '🤖'
          });
        }

        setSteps(workflowSteps);
      }
    } catch (error) {
      console.error('Failed to load workflow data:', error);
      // Show error state
      setSteps([{
        id: 'error',
        name: 'Error Loading Workflow',
        type: 'ai_generation',
        status: 'failed',
        description: 'Failed to load workflow data',
        icon: '❌'
      }]);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'approved': return 'bg-green-500';
      case 'running': return 'bg-blue-500';
      case 'failed': return 'bg-red-500';
      case 'rejected': return 'bg-red-500';
      case 'waiting_approval': return 'bg-yellow-500';
      case 'pending': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-700';
      case 'running': return 'text-blue-700';
      case 'failed': return 'text-red-700';
      case 'pending': return 'text-gray-700';
      default: return 'text-gray-700';
    }
  };

  const getStepIcon = (type: string, icon?: string) => {
    if (icon) return icon;
    switch (type) {
      case 'template': return '📋';
      case 'input': return '⚙️';
      case 'ai_generation': return '🤖';
      case 'review': return '👥';
      case 'approval_gate': return '✅';
      case 'output': return '📄';
      default: return '⚡';
    }
  };

  const handleStepClick = (step: WorkflowStep) => {
    setSelectedStep(step);
    if (onStepClick) {
      onStepClick(step.id, step.type);
    }
  };

  const updateStepStatus = (stepId: string, status: string, progress?: number) => {
    if (readOnly) return;

    setSteps(prevSteps =>
      prevSteps.map(step =>
        step.id === stepId
          ? { ...step, status: status as any, progress }
          : step
      )
    );
  };

  return (
    <div className="h-full w-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Workflow Visualization</h2>
            <p className="text-sm text-gray-600">
              {workflowId ? `Execution ID: ${workflowId}` : 'Interactive workflow progress'}
            </p>
          </div>

          <div className="flex items-center space-x-6">
            {/* Workflow Status Indicator */}
            <div className="flex items-center space-x-2">
              {steps.some(s => s.status === 'waiting_approval') ? (
                <>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-yellow-700">Waiting for Approval</span>
                </>
              ) : steps.some(s => s.status === 'running') ? (
                <>
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-blue-700">Running</span>
                </>
              ) : steps.every(s => s.status === 'completed' || s.status === 'approved') ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-green-700">Completed</span>
                </>
              ) : (
                <>
                  <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-600">Pending</span>
                </>
              )}
            </div>

            {/* Progress Counter */}
            <div className="text-sm text-gray-600">
              {steps.filter(s => s.status === 'completed' || s.status === 'approved').length} of {steps.length} steps completed
            </div>
          </div>
        </div>

        {/* Approval Alert Banner */}
        {steps.some(s => s.status === 'waiting_approval') && (
          <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Workflow Paused - Approval Required
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>The workflow is waiting for your approval to continue. Please review the artifacts below and approve or reject them.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Workflow Steps */}
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Connecting Line */}
            <div className="absolute left-8 top-16 bottom-0 w-0.5 bg-gray-300"></div>
            
            {/* Steps */}
            <div className="space-y-8">
              {steps.map((step, index) => (
                <div key={step.id} className="relative flex items-start">
                  {/* Step Circle */}
                  <div
                    className={`
                      relative z-10 flex items-center justify-center w-16 h-16 rounded-full border-4 border-white shadow-lg cursor-pointer transition-all duration-200
                      ${getStatusColor(step.status)}
                      ${selectedStep?.id === step.id ? 'ring-4 ring-blue-300' : ''}
                      ${step.type === 'approval_gate' && step.status === 'waiting_approval' ? 'ring-4 ring-yellow-400 animate-pulse' : ''}
                      hover:scale-105
                    `}
                    onClick={() => handleStepClick(step)}
                  >
                    <span className="text-2xl">{getStepIcon(step.type, step.icon)}</span>

                    {/* Status Indicators */}
                    {step.status === 'running' && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-pulse">
                        <div className="w-full h-full bg-blue-400 rounded-full animate-ping"></div>
                      </div>
                    )}

                    {/* Approval Gate Waiting Indicator */}
                    {step.type === 'approval_gate' && step.status === 'waiting_approval' && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center animate-bounce">
                        <span className="text-white text-xs font-bold">!</span>
                      </div>
                    )}

                    {/* Approval Gate Approved Indicator */}
                    {step.type === 'approval_gate' && step.status === 'approved' && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}

                    {/* Approval Gate Rejected Indicator */}
                    {step.type === 'approval_gate' && step.status === 'rejected' && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✗</span>
                      </div>
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="ml-6 flex-1 min-w-0">
                    <div 
                      className={`
                        bg-white rounded-lg border-2 p-4 shadow-sm cursor-pointer transition-all duration-200
                        ${selectedStep?.id === step.id ? 'border-blue-300 shadow-md' : 'border-gray-200'}
                        hover:border-gray-300 hover:shadow-md
                      `}
                      onClick={() => handleStepClick(step)}
                    >
                      {/* Step Header */}
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{step.name}</h3>
                        <span className={`
                          px-3 py-1 rounded-full text-xs font-medium
                          ${step.status === 'completed' ? 'bg-green-100 text-green-800' : ''}
                          ${step.status === 'approved' ? 'bg-green-100 text-green-800' : ''}
                          ${step.status === 'running' ? 'bg-blue-100 text-blue-800' : ''}
                          ${step.status === 'failed' ? 'bg-red-100 text-red-800' : ''}
                          ${step.status === 'rejected' ? 'bg-red-100 text-red-800' : ''}
                          ${step.status === 'waiting_approval' ? 'bg-yellow-100 text-yellow-800' : ''}
                          ${step.status === 'pending' ? 'bg-gray-100 text-gray-800' : ''}
                        `}>
                          {step.status.charAt(0).toUpperCase() + step.status.slice(1)}
                        </span>
                      </div>

                      {/* Step Description */}
                      {step.description && (
                        <p className="text-sm text-gray-600 mb-3">{step.description}</p>
                      )}

                      {/* Progress Bar */}
                      {step.status === 'running' && step.progress !== undefined && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs text-gray-600">Progress</span>
                            <span className="text-xs font-medium text-gray-900">{step.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${step.progress}%` }}
                            />
                          </div>
                        </div>
                      )}

                      {/* Step Metadata */}
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-4">
                          <span className="text-gray-500">Type: {step.type.replace('_', ' ')}</span>
                          {step.reviewers && step.reviewers.length > 0 && (
                            <span className="text-gray-500">
                              Reviewers: {step.reviewers.length}
                            </span>
                          )}
                          {step.priority && (
                            <span className={`
                              px-2 py-1 rounded text-xs font-medium
                              ${step.priority === 'high' ? 'bg-red-100 text-red-800' : ''}
                              ${step.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : ''}
                              ${step.priority === 'low' ? 'bg-green-100 text-green-800' : ''}
                            `}>
                              {step.priority} priority
                            </span>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex space-x-2">
                          {/* Approval Gate - Special Handling */}
                          {step.type === 'approval_gate' && step.status === 'waiting_approval' && step.approvalUrl && (
                            <div className="flex space-x-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(step.approvalUrl, '_blank');
                                }}
                                className="px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-lg hover:bg-yellow-700 transition-colors shadow-lg animate-pulse"
                              >
                                🔍 Review & Approve
                              </button>
                              <div className="flex items-center text-xs text-yellow-700 bg-yellow-100 px-2 py-1 rounded">
                                ⏸️ Workflow Paused
                              </div>
                            </div>
                          )}

                          {/* Regular Action Buttons for Non-Readonly Mode */}
                          {!readOnly && step.type !== 'approval_gate' && (
                            <>
                              {step.status === 'pending' && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    updateStepStatus(step.id, 'running', 0);
                                  }}
                                  className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                                >
                                  Start
                                </button>
                              )}

                              {step.status === 'running' && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    updateStepStatus(step.id, 'completed', 100);
                                  }}
                                  className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                                >
                                  Complete
                                </button>
                              )}

                              {step.type === 'review' && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    window.open(`/review/${step.id}`, '_blank');
                                  }}
                                  className="px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700"
                                >
                                  Review
                                </button>
                              )}
                            </>
                          )}

                          {/* Status Indicators for Completed Approval Gates */}
                          {step.type === 'approval_gate' && step.status === 'approved' && (
                            <div className="flex items-center text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                              ✅ Approved
                            </div>
                          )}

                          {step.type === 'approval_gate' && step.status === 'rejected' && (
                            <div className="flex items-center text-xs text-red-700 bg-red-100 px-2 py-1 rounded">
                              ❌ Rejected
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Step Details Sidebar */}
      {selectedStep && (
        <div className="absolute top-0 right-0 w-80 h-full bg-white border-l border-gray-200 shadow-lg overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Step Details</h3>
              <button
                onClick={() => setSelectedStep(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <p className="mt-1 text-sm text-gray-900">{selectedStep.name}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Type</label>
                <p className="mt-1 text-sm text-gray-900">{selectedStep.type.replace('_', ' ')}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1 flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(selectedStep.status)}`} />
                  <span className="text-sm text-gray-900 capitalize">{selectedStep.status}</span>
                </div>
              </div>

              {selectedStep.description && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedStep.description}</p>
                </div>
              )}

              {selectedStep.progress !== undefined && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Progress</label>
                  <div className="mt-1">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${selectedStep.progress}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{selectedStep.progress}%</p>
                  </div>
                </div>
              )}

              {selectedStep.reviewers && selectedStep.reviewers.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reviewers</label>
                  <div className="mt-1 space-y-1">
                    {selectedStep.reviewers.map((reviewer, index) => (
                      <div key={index} className="text-sm text-gray-900 bg-gray-50 px-2 py-1 rounded">
                        {reviewer}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
